import { NextResponse } from "next/server";
import type {
  SearchResponse,
  SearchResult,
  GeocodingApiResponse,
  GeocodingApiResult
} from "@/lib/types";
import {
  sanitizeSearchQuery,
  isValidSearchQuery,
  processSearchResults,
  MAX_SEARCH_RESULTS
} from "@/lib/search/utils";

/**
 * Validate and transform geocoding API result to SearchResult
 */
function transformGeocodingResult(result: GeocodingApiResult): SearchResult {
  return {
    name: result.name,
    country: result.country ?? "",
    admin1: result.admin1 ?? "",
    latitude: result.latitude,
    longitude: result.longitude,
  };
}

/**
 * Validate geocoding API result
 */
function isValidGeocodingResult(result: any): result is GeocodingApiResult {
  return (
    typeof result === 'object' &&
    result !== null &&
    typeof result.name === 'string' &&
    result.name.trim().length > 0 &&
    typeof result.latitude === 'number' &&
    typeof result.longitude === 'number' &&
    !isNaN(result.latitude) &&
    !isNaN(result.longitude) &&
    result.latitude >= -90 &&
    result.latitude <= 90 &&
    result.longitude >= -180 &&
    result.longitude <= 180
  );
}

export async function GET(request: Request): Promise<NextResponse<SearchResponse>> {
  try {
    const { searchParams } = new URL(request.url);
    const rawQuery = searchParams.get("q");

    if (!rawQuery) {
      return NextResponse.json({ results: [] });
    }

    const query = sanitizeSearchQuery(rawQuery);

    if (!isValidSearchQuery(query)) {
      return NextResponse.json({ results: [] });
    }

    const apiUrl = new URL("https://geocoding-api.open-meteo.com/v1/search");
    apiUrl.searchParams.set("name", query);
    apiUrl.searchParams.set("count", MAX_SEARCH_RESULTS.toString());
    apiUrl.searchParams.set("language", "en");
    apiUrl.searchParams.set("format", "json");

    const response = await fetch(apiUrl.toString(), {
      cache: "no-store",
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'Weather-App/1.0'
      }
    });

    if (!response.ok) {
      console.error(`Geocoding API error: ${response.status} ${response.statusText}`);
      return NextResponse.json({ results: [] });
    }

    const data: GeocodingApiResponse = await response.json();

    if (!data.results || !Array.isArray(data.results)) {
      return NextResponse.json({ results: [] });
    }

    // Filter and validate results
    const validResults = data.results
      .filter(isValidGeocodingResult)
      .map(transformGeocodingResult);

    // Process results (deduplicate, limit, etc.)
    const processedResults = processSearchResults(validResults);

    return NextResponse.json({ results: processedResults });

  } catch (error) {
    console.error('Search API error:', error);
    return NextResponse.json({ results: [] });
  }
}


