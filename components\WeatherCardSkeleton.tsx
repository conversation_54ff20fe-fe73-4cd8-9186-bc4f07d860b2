"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

/**
 * Loading skeleton for WeatherCard component
 */
export function WeatherCardSkeleton() {
  return (
    <Card className="w-full max-w-4xl overflow-hidden border border-border shadow-xl">
      <CardContent className="p-0">
        <div className="flex flex-col lg:grid lg:grid-cols-2">
          {/* Image Skeleton */}
          <div className="relative h-48 sm:h-56 md:h-64 lg:h-80">
            <Skeleton className="w-full h-full" />
          </div>
          
          {/* Content Skeleton */}
          <div className="p-4 sm:p-6 flex flex-col gap-4">
            {/* Header Skeleton */}
            <div className="space-y-2">
              <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                <Skeleton className="h-8 w-48" />
                <Skeleton className="h-5 w-16" />
              </div>
              <Skeleton className="h-5 w-32" />
            </div>
            
            {/* Main Stats Skeleton */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
              <div className="rounded-lg bg-secondary p-3 sm:p-4 space-y-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-8 w-16" />
              </div>
              <div className="rounded-lg bg-secondary p-3 sm:p-4 space-y-2">
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-8 w-20" />
              </div>
            </div>
            
            {/* Separator */}
            <Skeleton className="h-px w-full" />
            
            {/* Additional Details Skeleton */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
              {[1, 2, 3].map((i) => (
                <div key={i} className="rounded-lg bg-secondary p-3 space-y-2">
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-6 w-12" />
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
