/**
 * Search API utilities and functions
 */

import type { SearchResponse, SearchResult, GeocodingApiResponse, ApiError } from "@/lib/types";

/**
 * Search for locations using the geocoding API
 */
export async function searchLocations(
  query: string,
  signal?: AbortSignal
): Promise<SearchResult[]> {
  if (!query.trim()) {
    return [];
  }

  try {
    const response = await fetch(
      `/api/search?q=${encodeURIComponent(query)}`,
      { 
        signal,
        cache: "no-store"
      }
    );

    if (!response.ok) {
      throw new Error(`Search failed: ${response.status}`);
    }

    const data: SearchResponse = await response.json();
    return data.results || [];
  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      // Request was cancelled, don't throw
      return [];
    }
    
    console.error('Search API error:', error);
    throw new Error('Failed to search locations');
  }
}

/**
 * Validate search result data
 */
export function isValidSearchResult(result: any): result is SearchResult {
  return (
    typeof result === 'object' &&
    result !== null &&
    typeof result.name === 'string' &&
    typeof result.country === 'string' &&
    typeof result.admin1 === 'string' &&
    typeof result.latitude === 'number' &&
    typeof result.longitude === 'number' &&
    !isNaN(result.latitude) &&
    !isNaN(result.longitude)
  );
}

/**
 * Format search result for display
 */
export function formatSearchResult(result: SearchResult): string {
  const parts = [result.admin1, result.country].filter(Boolean);
  return parts.length > 0 ? `${result.name}, ${parts.join(", ")}` : result.name;
}

/**
 * Create a unique key for a search result
 */
export function getSearchResultKey(result: SearchResult, index?: number): string {
  return `${result.name}-${result.latitude}-${result.longitude}-${index ?? 0}`;
}

/**
 * Check if two search results are the same location
 */
export function areSearchResultsEqual(a: SearchResult, b: SearchResult): boolean {
  return (
    a.name === b.name &&
    a.latitude === b.latitude &&
    a.longitude === b.longitude &&
    a.country === b.country
  );
}
