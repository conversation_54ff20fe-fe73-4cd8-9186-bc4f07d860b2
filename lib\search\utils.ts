/**
 * Search utility functions
 */

import type { SearchResult } from "@/lib/types";

/**
 * Default debounce delay for search input (in milliseconds)
 */
export const DEFAULT_SEARCH_DEBOUNCE_DELAY = 250;

/**
 * Maximum number of search results to display
 */
export const MAX_SEARCH_RESULTS = 7;

/**
 * Minimum query length to trigger search
 */
export const MIN_SEARCH_QUERY_LENGTH = 1;

/**
 * Sanitize search query by trimming whitespace and limiting length
 */
export function sanitizeSearchQuery(query: string): string {
  return query.trim().slice(0, 100); // Limit to 100 characters
}

/**
 * Check if a search query is valid
 */
export function isValidSearchQuery(query: string): boolean {
  const sanitized = sanitizeSearchQuery(query);
  return sanitized.length >= MIN_SEARCH_QUERY_LENGTH;
}

/**
 * Filter and deduplicate search results
 */
export function processSearchResults(results: SearchResult[]): SearchResult[] {
  // Remove duplicates based on coordinates and name
  const seen = new Set<string>();
  const filtered = results.filter((result) => {
    const key = `${result.name}-${result.latitude}-${result.longitude}`;
    if (seen.has(key)) {
      return false;
    }
    seen.add(key);
    return true;
  });

  // Limit to maximum results
  return filtered.slice(0, MAX_SEARCH_RESULTS);
}

/**
 * Sort search results by relevance (exact matches first, then alphabetical)
 */
export function sortSearchResults(results: SearchResult[], query: string): SearchResult[] {
  const lowerQuery = query.toLowerCase();
  
  return [...results].sort((a, b) => {
    const aName = a.name.toLowerCase();
    const bName = b.name.toLowerCase();
    
    // Exact matches first
    const aExact = aName === lowerQuery;
    const bExact = bName === lowerQuery;
    
    if (aExact && !bExact) return -1;
    if (!aExact && bExact) return 1;
    
    // Starts with query second
    const aStarts = aName.startsWith(lowerQuery);
    const bStarts = bName.startsWith(lowerQuery);
    
    if (aStarts && !bStarts) return -1;
    if (!aStarts && bStarts) return 1;
    
    // Alphabetical order
    return aName.localeCompare(bName);
  });
}

/**
 * Highlight matching text in search results
 */
export function highlightSearchMatch(text: string, query: string): string {
  if (!query.trim()) return text;
  
  const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
  return text.replace(regex, '<mark>$1</mark>');
}

/**
 * Get search result display text
 */
export function getSearchResultDisplayText(result: SearchResult): {
  primary: string;
  secondary: string;
} {
  return {
    primary: result.name,
    secondary: [result.admin1, result.country].filter(Boolean).join(", ")
  };
}

/**
 * Create search history key for localStorage
 */
export function getSearchHistoryKey(): string {
  return 'weather-app-search-history';
}

/**
 * Validate coordinates
 */
export function isValidCoordinates(lat: number, lon: number): boolean {
  return (
    typeof lat === 'number' &&
    typeof lon === 'number' &&
    !isNaN(lat) &&
    !isNaN(lon) &&
    lat >= -90 &&
    lat <= 90 &&
    lon >= -180 &&
    lon <= 180
  );
}
