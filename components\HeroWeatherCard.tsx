"use client";

import Image from "next/image";
import { Card, CardContent } from "@/components/ui/card";
import { Wind, Droplets, Gauge, MapPin } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import type { Condition, Units } from "@/lib/types";

export type HeroWeatherProps = {
  image: string;
  condition: Condition;
  isDay: number | boolean;
  name: string;
  country?: string;
  temperature: number;
  unit: Units;
  onUnitChange?: (unit: Units) => void;
  windspeed?: number | null;
  humidity?: number | null;
  pressure?: number | null;
};

function getWeatherClasses(condition: Condition, isDay: number | boolean) {
  const day = typeof isDay === "boolean" ? (isDay ? 1 : 0) : isDay;
  const isDaytime = day === 1;

  if (!isDaytime) {
    return "weather-night text-white";
  }

  switch (condition) {
    case "Rain":
      return "weather-rainy rain-animation text-white";
    case "Cloudy":
      return "weather-cloudy floating-clouds text-gray-800";
    case "Snow":
      return "weather-snowy snow-animation text-gray-800";
    case "Fog":
      return "weather-cloudy text-gray-800";
    default:
      return "weather-sunny text-white";
  }
}

export function HeroWeatherCard(props: HeroWeatherProps) {
  const weatherClasses = getWeatherClasses(props.condition, props.isDay);
  const unitSuffix = props.unit === "imperial" ? "°F" : "°C";

  return (
    <Card className="rounded-3xl border-0 shadow-2xl overflow-hidden relative">
      <CardContent className="p-0">
        <div className={`relative ${weatherClasses} min-h-[320px] sm:min-h-[360px]`}>
          {/* Animated Background Overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-black/10 via-transparent to-black/20 pointer-events-none" />

          {/* Main Content */}
          <div className="relative z-10 p-6 sm:p-8">
            {/* Header with location and unit toggle */}
            <div className="flex items-start justify-between mb-8">
              <div className="flex items-center gap-2">
                <MapPin className="h-5 w-5 opacity-90" />
                <span className="text-body font-medium opacity-90">
                  {props.country ? `${props.name}, ${props.country}` : props.name}
                </span>
              </div>

              {/* Unit toggle (Metric / Imperial) */}
              <div className="glass-card rounded-full px-3 py-1.5 flex items-center gap-3 text-white">
                <span className={`text-body-sm ${props.unit === "metric" ? "opacity-100" : "opacity-70"}`}>°C / km/h</span>
                <Switch
                  checked={props.unit === "imperial"}
                  onCheckedChange={(checked) => props.onUnitChange?.(checked ? "imperial" : "metric")}
                  aria-label="Toggle temperature unit"
                />
                <span className={`text-body-sm ${props.unit === "imperial" ? "opacity-100" : "opacity-70"}`}>°F / mph</span>
              </div>
            </div>

            {/* Temperature and Hero Image */}
            <div className="relative mb-8 min-h-[160px] sm:min-h-[200px]">
              {/* Right image occupies approx half of the card */}
              <div className="absolute inset-y-0 right-0 w-1/2 flex items-center justify-end pr-2 sm:pr-4 md:pr-6">
                <div className="weather-image-circle h-40 sm:h-48 md:h-56 aspect-square glass-card p-0">
                  <div className="relative w-full h-full">
                    <Image
                      src={props.image}
                      alt={props.condition}
                      fill
                      className="object-cover"
                      priority
                    />
                  </div>
                </div>
              </div>

              {/* Left temperature text */}
              <div className="relative z-10 max-w-[50%]">
                <div className="text-6xl sm:text-7xl md:text-8xl font-bold leading-none drop-shadow-lg">
                  {Math.round(props.temperature)}
                  <span className="text-4xl sm:text-5xl md:text-6xl opacity-80">{unitSuffix}</span>
                </div>
                <div className="mt-2 text-lg sm:text-xl opacity-90 font-medium">
                  {props.condition}
                </div>
              </div>
            </div>

            {/* Weather Metrics */}
            <div className="grid grid-cols-3 gap-3 sm:gap-4">
              <div className="glass-card rounded-2xl p-4 text-center hover:scale-105 transition-transform duration-300">
                <div className="flex items-center justify-center gap-2 mb-2 opacity-90">
                  <Wind className="h-4 w-4" />
                  <span className="text-caption font-medium">Wind</span>
                </div>
                <div className="text-body font-bold">
                  {props.windspeed != null ? (
                    <>{Math.round(props.windspeed)} {props.unit === "imperial" ? "mph" : "km/h"}</>
                  ) : (
                    <>-</>
                  )}
                </div>
              </div>

              <div className="glass-card rounded-2xl p-4 text-center hover:scale-105 transition-transform duration-300">
                <div className="flex items-center justify-center gap-2 mb-2 opacity-90">
                  <Droplets className="h-4 w-4" />
                  <span className="text-caption font-medium">Humidity</span>
                </div>
                <div className="text-body font-bold">
                  {props.humidity != null ? <>{Math.round(props.humidity)}%</> : <>-</>}
                </div>
              </div>

              <div className="glass-card rounded-2xl p-4 text-center hover:scale-105 transition-transform duration-300">
                <div className="flex items-center justify-center gap-2 mb-2 opacity-90">
                  <Gauge className="h-4 w-4" />
                  <span className="text-caption font-medium">Pressure</span>
                </div>
                <div className="text-body font-bold">
                  {props.pressure != null ? <>{Math.round(props.pressure)} hPa</> : <>-</>}
                </div>
              </div>
            </div>
          </div>

          {/* Decorative Elements */}
          <div className="absolute top-4 right-4 w-20 h-20 rounded-full bg-white/5 blur-2xl pointer-events-none" />
          <div className="absolute bottom-4 left-4 w-16 h-16 rounded-full bg-white/5 blur-xl pointer-events-none" />
        </div>
      </CardContent>
    </Card>
  );
}

export default HeroWeatherCard;

