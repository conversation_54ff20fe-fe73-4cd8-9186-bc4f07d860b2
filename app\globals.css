@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* Typography Scale Integration */
  --font-size-xs: var(--font-size-xs);
  --font-size-sm: var(--font-size-sm);
  --font-size-base: var(--font-size-base);
  --font-size-lg: var(--font-size-lg);
  --font-size-xl: var(--font-size-xl);
  --font-size-2xl: var(--font-size-2xl);
  --font-size-3xl: var(--font-size-3xl);
  --font-size-4xl: var(--font-size-4xl);
  --font-size-5xl: var(--font-size-5xl);
  --font-size-6xl: var(--font-size-6xl);
  --font-size-7xl: var(--font-size-7xl);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;

  /* Typography Scale - Modular scale with 1.25 ratio (major third) */
  --font-size-xs: 0.75rem;      /* 12px */
  --font-size-sm: 0.875rem;     /* 14px */
  --font-size-base: 1rem;       /* 16px - minimum accessible size */
  --font-size-lg: 1.125rem;     /* 18px */
  --font-size-xl: 1.25rem;      /* 20px */
  --font-size-2xl: 1.5rem;      /* 24px */
  --font-size-3xl: 1.875rem;    /* 30px */
  --font-size-4xl: 2.25rem;     /* 36px */
  --font-size-5xl: 3rem;        /* 48px */
  --font-size-6xl: 3.75rem;     /* 60px */
  --font-size-7xl: 4.5rem;      /* 72px */

  /* Line Heights - Optimized for readability */
  --line-height-tight: 1.25;    /* For large headings */
  --line-height-snug: 1.375;    /* For headings */
  --line-height-normal: 1.5;    /* For body text */
  --line-height-relaxed: 1.625; /* For large body text */
  --line-height-loose: 2;       /* For captions */

  /* Letter Spacing */
  --letter-spacing-tighter: -0.05em;
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0em;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;

  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  /* Modern light palette: clean whites, subtle grays, blue accent */
  --primary: oklch(0.58 0.13 262);              /* Accent blue */
  --primary-foreground: oklch(0.985 0 0);       /* Near white */
  --secondary: oklch(0.975 0 0);                /* Subtle gray background */
  --secondary-foreground: oklch(0.205 0 0);     /* Dark text */
  --muted: oklch(0.975 0 0);
  --muted-foreground: oklch(0.5 0 0);
  --accent: oklch(0.975 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.93 0 0);
  --input: oklch(0.93 0 0);
  --ring: oklch(0.58 0.13 262 / 60%);           /* Blue ring */
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  /* Dark theme tuned to the same accent */
  --background: oklch(0.15 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.22 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.22 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.65 0.16 262);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.28 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.28 0 0);
  --muted-foreground: oklch(0.75 0 0);
  --accent: oklch(0.28 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 12%);
  --input: oklch(1 0 0 / 16%);
  --ring: oklch(0.65 0.16 262 / 50%);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.22 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.65 0.16 262);
  --sidebar-primary-foreground: oklch(0.205 0 0);
  --sidebar-accent: oklch(0.28 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 12%);
  --sidebar-ring: oklch(0.65 0.16 262 / 50%);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
  }

  /* Typography Hierarchy - Systematic scale with responsive sizing */
  h1 {
    font-size: var(--font-size-3xl);
    line-height: var(--line-height-tight);
    letter-spacing: var(--letter-spacing-tight);
    font-weight: 600;
  }

  h2 {
    font-size: var(--font-size-2xl);
    line-height: var(--line-height-snug);
    letter-spacing: var(--letter-spacing-tight);
    font-weight: 600;
  }

  h3 {
    font-size: var(--font-size-xl);
    line-height: var(--line-height-snug);
    letter-spacing: var(--letter-spacing-normal);
    font-weight: 500;
  }

  h4 {
    font-size: var(--font-size-lg);
    line-height: var(--line-height-snug);
    letter-spacing: var(--letter-spacing-normal);
    font-weight: 500;
  }

  /* Responsive typography scaling */
  @media (min-width: 640px) {
    h1 { font-size: var(--font-size-4xl); }
    h2 { font-size: var(--font-size-3xl); }
    h3 { font-size: var(--font-size-2xl); }
    h4 { font-size: var(--font-size-xl); }
  }

  @media (min-width: 1024px) {
    h1 { font-size: var(--font-size-5xl); }
    h2 { font-size: var(--font-size-4xl); }
  }
}

/* Elevation helpers */
.shadow-card {
  box-shadow: 0 6px 24px rgba(0,0,0,0.06), 0 2px 8px rgba(0,0,0,0.04);
}

/* Enhanced visual design utilities */
.glass-effect {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass-effect {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Smooth transitions for all interactive elements */
.transition-smooth {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced focus styles for better accessibility */
.focus-ring {
  @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
}

/* Custom scrollbar styling */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* Typography Utility Classes */
.text-display-sm {
  font-size: var(--font-size-4xl);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-tight);
  font-weight: 700;
}

.text-display-md {
  font-size: var(--font-size-5xl);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-tight);
  font-weight: 700;
}

.text-display-lg {
  font-size: var(--font-size-6xl);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-tighter);
  font-weight: 700;
}

.text-display-xl {
  font-size: var(--font-size-7xl);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-tighter);
  font-weight: 700;
}

.text-heading-sm {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-snug);
  letter-spacing: var(--letter-spacing-normal);
  font-weight: 500;
}

.text-heading-md {
  font-size: var(--font-size-xl);
  line-height: var(--line-height-snug);
  letter-spacing: var(--letter-spacing-normal);
  font-weight: 600;
}

.text-heading-lg {
  font-size: var(--font-size-2xl);
  line-height: var(--line-height-snug);
  letter-spacing: var(--letter-spacing-tight);
  font-weight: 600;
}

.text-body-sm {
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
  letter-spacing: var(--letter-spacing-normal);
}

.text-body {
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  letter-spacing: var(--letter-spacing-normal);
}

.text-body-lg {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
  letter-spacing: var(--letter-spacing-normal);
}

.text-caption {
  font-size: var(--font-size-xs);
  line-height: var(--line-height-loose);
  letter-spacing: var(--letter-spacing-wide);
}

/* Responsive typography utilities */
@media (min-width: 640px) {
  .text-display-sm { font-size: var(--font-size-5xl); }
  .text-display-md { font-size: var(--font-size-6xl); }
  .text-display-lg { font-size: var(--font-size-7xl); }
  .text-heading-md { font-size: var(--font-size-2xl); }
  .text-heading-lg { font-size: var(--font-size-3xl); }
}

@media (min-width: 1024px) {
  .text-display-md { font-size: var(--font-size-7xl); }
  .text-heading-lg { font-size: var(--font-size-4xl); }
}

/* Weather Background Animations */
@keyframes float-clouds {
  0%, 100% { transform: translateX(0) translateY(0); }
  33% { transform: translateX(10px) translateY(-5px); }
  66% { transform: translateX(-5px) translateY(5px); }
}

@keyframes rain-drops {
  0% { transform: translateY(-100vh) rotate(10deg); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateY(100vh) rotate(10deg); opacity: 0; }
}

@keyframes snow-fall {
  0% { transform: translateY(-100vh) rotate(0deg); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateY(100vh) rotate(360deg); opacity: 0; }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 20px rgba(255, 255, 255, 0.1); }
  50% { box-shadow: 0 0 40px rgba(255, 255, 255, 0.2); }
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Weather Animation Classes */
.weather-sunny {
  background: linear-gradient(-45deg, #FFD700, #FFA500, #FF8C00, #FF6347);
  background-size: 400% 400%;
  animation: gradient-shift 8s ease infinite;
}

.weather-cloudy {
  background: linear-gradient(-45deg, #87CEEB, #B0C4DE, #D3D3D3, #F0F8FF);
  background-size: 400% 400%;
  animation: gradient-shift 12s ease infinite;
}

.weather-rainy {
  background: linear-gradient(-45deg, #4682B4, #5F9EA0, #708090, #778899);
  background-size: 400% 400%;
  animation: gradient-shift 6s ease infinite;
}

.weather-snowy {
  background: linear-gradient(-45deg, #F0F8FF, #E6E6FA, #D8BFD8, #DDA0DD);
  background-size: 400% 400%;
  animation: gradient-shift 10s ease infinite;
}

.weather-night {
  background: linear-gradient(-45deg, #191970, #483D8B, #2F4F4F, #1e1e2e);
  background-size: 400% 400%;
  animation: gradient-shift 15s ease infinite;
}

/* Floating Elements */
.floating-clouds {
  animation: float-clouds 20s ease-in-out infinite;
}

.rain-animation::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(transparent 0%, rgba(255,255,255,0.1) 50%, transparent 100%);
  background-size: 2px 20px;
  background-repeat: repeat;
  animation: rain-drops 1s linear infinite;
  pointer-events: none;
}

.snow-animation::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle, rgba(255,255,255,0.8) 1px, transparent 1px);
  background-size: 20px 20px;
  animation: snow-fall 3s linear infinite;
  pointer-events: none;
}

/* Glass Morphism Effects */
.glass-card {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.glass-card-dark {
  backdrop-filter: blur(20px);
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Image Container Shapes */
.weather-image-circle {
  border-radius: 50%;
  overflow: hidden;
  position: relative;
}

.weather-image-hexagon {
  clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
  position: relative;
}

.weather-image-rounded {
  border-radius: 2rem;
  overflow: hidden;
  position: relative;
}

/* Improved text rendering */
.text-crisp {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}
