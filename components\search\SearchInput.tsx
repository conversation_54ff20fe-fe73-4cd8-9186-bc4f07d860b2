"use client";

import React from "react";
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import type { SearchInputProps } from "@/lib/types";

/**
 * Search input component with icon and loading state
 */
export function SearchInput({
  value,
  onChange,
  onFocus,
  onKeyDown,
  placeholder = "Search city or ZIP",
  isLoading = false,
  className
}: SearchInputProps) {
  return (
    <div className={cn("relative flex-1", className)}>
      <Input
        type="text"
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onFocus={onFocus}
        onKeyDown={onKeyDown}
        disabled={isLoading}
        className="pr-10"
        aria-label="Search for a city or ZIP code"
        aria-describedby="search-instructions"
        autoComplete="off"
        spellCheck={false}
      />
      <Search 
        className={cn(
          "absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground transition-opacity",
          isLoading && "opacity-50"
        )}
        aria-hidden="true"
      />
      <div id="search-instructions" className="sr-only">
        Type to search for cities or ZIP codes. Use arrow keys to navigate suggestions.
      </div>
    </div>
  );
}
