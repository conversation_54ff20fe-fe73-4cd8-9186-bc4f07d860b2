"use client";

import React, { useState, useCallback, useRef, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { Search, MapPin, Loader2, LocateFixed } from "lucide-react";
import type { SearchBoxProps, SearchResult } from "@/lib/types";
import { useSearchSuggestions } from "@/lib/search";
import { LocationButton } from "./LocationButton";

/**
 * Simplified search box with better UX
 */
export function SearchBox({
  unit,
  loading,
  onSelectQuery,
  onSelectCoords,
  className,
  placeholder = "Search for a city..."
}: SearchBoxProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  const {
    results,
    isLoading: suggestionsLoading,
    error,
    searchValue,
    setSearchValue,
    clearResults
  } = useSearchSuggestions();

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node) &&
        !inputRef.current?.contains(event.target as Node)
      ) {
        setIsOpen(false);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);
    setSelectedIndex(-1);

    if (value.trim()) {
      setIsOpen(true);
    } else {
      setIsOpen(false);
      clearResults();
    }
  }, [setSearchValue, clearResults]);

  const handleInputFocus = useCallback(() => {
    if (searchValue.trim() && results.length > 0) {
      setIsOpen(true);
    }
  }, [searchValue, results.length]);

  const handleKeyDown = useCallback((event: React.KeyboardEvent<HTMLInputElement>) => {
    if (!isOpen && event.key !== "Enter") return;

    switch (event.key) {
      case "ArrowDown":
        event.preventDefault();
        setSelectedIndex(prev =>
          prev < results.length - 1 ? prev + 1 : prev
        );
        break;
      case "ArrowUp":
        event.preventDefault();
        setSelectedIndex(prev => prev > -1 ? prev - 1 : -1);
        break;
      case "Enter":
        event.preventDefault();
        if (selectedIndex >= 0 && results[selectedIndex]) {
          handleSuggestionSelect(results[selectedIndex]);
        } else if (searchValue.trim()) {
          handleSearch();
        }
        break;
      case "Escape":
        setIsOpen(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  }, [isOpen, results, selectedIndex, searchValue]);

  const handleSearch = useCallback(() => {
    const trimmedValue = searchValue.trim();
    if (trimmedValue) {
      setIsOpen(false);
      setSelectedIndex(-1);
      onSelectQuery(trimmedValue);
    }
  }, [searchValue, onSelectQuery]);

  const handleSuggestionSelect = useCallback((result: SearchResult) => {
    setSearchValue(result.name);
    setIsOpen(false);
    setSelectedIndex(-1);
    onSelectQuery(result.name);
  }, [setSearchValue, onSelectQuery]);

  const handleLocationSelect = useCallback((latitude: number, longitude: number) => {
    setSearchValue("");
    setIsOpen(false);
    setSelectedIndex(-1);
    clearResults();
    onSelectCoords(latitude, longitude);
  }, [setSearchValue, clearResults, onSelectCoords]);

  return (
    <div className={cn("relative w-full", className)}>
      {/* Main Search Input */}
      <div className="flex gap-2">
        <div className="relative flex-1">
          <Input
            ref={inputRef}
            type="text"
            placeholder={placeholder}
            value={searchValue}
            onChange={handleInputChange}
            onFocus={handleInputFocus}
            onKeyDown={handleKeyDown}
            disabled={loading}
            className="pr-10"
            aria-label="Search for a city"
            autoComplete="off"
            spellCheck={false}
          />

          {/* Search Icon */}
          <div className="absolute right-3 top-1/2 -translate-y-1/2">
            {suggestionsLoading ? (
              <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
            ) : (
              <Search className="h-4 w-4 text-muted-foreground" />
            )}
          </div>
        </div>

        {/* Search Button */}
        <Button
          onClick={handleSearch}
          disabled={loading || !searchValue.trim()}
          className="shrink-0"
          aria-label={loading ? "Searching..." : "Search"}
        >
          {loading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            "Search"
          )}
        </Button>

        {/* Location Button */}
        <LocationButton
          onLocationSelect={handleLocationSelect}
        />
      </div>

      {/* Search Suggestions Dropdown */}
      {isOpen && (searchValue.trim() || results.length > 0) && (
        <div
          ref={suggestionsRef}
          className="absolute top-full left-0 right-0 z-50 mt-1 bg-popover border border-border rounded-md shadow-lg max-h-60 overflow-auto"
        >
          {suggestionsLoading && (
            <div className="px-4 py-3 text-body-sm text-muted-foreground flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              Searching...
            </div>
          )}

          {!suggestionsLoading && results.length === 0 && searchValue.trim() && (
            <div className="px-4 py-3 text-body-sm text-muted-foreground">
              No locations found for "{searchValue}"
            </div>
          )}

          {results.length > 0 && (
            <div className="py-1">
              {results.map((result, index) => (
                <button
                  key={`${result.name}-${result.country}-${index}`}
                  onClick={() => handleSuggestionSelect(result)}
                  className={cn(
                    "w-full px-4 py-2 text-left hover:bg-accent hover:text-accent-foreground transition-colors",
                    "flex items-center gap-2 text-body-sm",
                    selectedIndex === index && "bg-accent text-accent-foreground"
                  )}
                  aria-label={`Select ${result.name}, ${result.admin1 || result.country}`}
                >
                  <MapPin className="h-3 w-3 text-muted-foreground shrink-0" />
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate text-body">{result.name}</div>
                    {(result.admin1 || result.country) && (
                      <div className="text-caption text-muted-foreground truncate">
                        {[result.admin1, result.country].filter(Boolean).join(", ")}
                      </div>
                    )}
                  </div>
                </button>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="absolute top-full left-0 right-0 z-50 mt-1 p-3 bg-destructive/10 border border-destructive/20 rounded-md text-body-sm text-destructive">
          {error}
        </div>
      )}
    </div>
  );
}
