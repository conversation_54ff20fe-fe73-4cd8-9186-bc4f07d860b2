"use client";

import React, { useState, useEffect } from "react";
import { LocateFixed, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import type { LocationButtonProps } from "@/lib/types";
import { useGeolocation } from "@/lib/search";

/**
 * Location button component for getting current location
 */
export function LocationButton({
  onLocationSelect,
  isLoading: externalLoading = false,
  className
}: LocationButtonProps) {
  const [mounted, setMounted] = useState(false);
  const { getCurrentLocation, isLoading: geoLoading, error, isSupported } = useGeolocation();

  // Prevent hydration mismatch by only showing dynamic content after mount
  useEffect(() => {
    setMounted(true);
  }, []);

  const isLoading = externalLoading || geoLoading;
  const isDisabled = isLoading || (!mounted ? false : !isSupported);

  const handleLocationClick = async () => {
    if (isDisabled) return;

    try {
      const coords = await getCurrentLocation();
      if (coords) {
        onLocationSelect(coords.latitude, coords.longitude);
      }
    } catch (err) {
      // Error is already handled by the hook
      console.error('Failed to get location:', err);
    }
  };

  const getButtonTitle = () => {
    if (!mounted) return "Use current location";
    if (!isSupported) return "Geolocation not supported";
    if (error) return `Location error: ${error}`;
    if (isLoading) return "Getting location...";
    return "Use current location";
  };

  const buttonTitle = getButtonTitle();

  return (
    <Button
      variant="secondary"
      size="default"
      onClick={handleLocationClick}
      disabled={isDisabled}
      title={buttonTitle}
      className={cn("shrink-0", className)}
      aria-label={buttonTitle}
    >
      {isLoading ? (
        <Loader2 className="h-4 w-4 animate-spin" aria-hidden="true" />
      ) : (
        <LocateFixed className="h-4 w-4" aria-hidden="true" />
      )}
      <span className="sr-only">{buttonTitle}</span>
    </Button>
  );
}
