"use client";

import React from "react";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import type { SearchSuggestionsProps } from "@/lib/types";
import { getSearchResultKey, getSearchResultDisplayText } from "@/lib/search";

/**
 * Search suggestions dropdown component
 */
export function SearchSuggestions({
  isOpen,
  onOpenChange,
  results,
  isLoading,
  onSelect,
  searchValue,
  onSearchValueChange,
  children
}: SearchSuggestionsProps & { children: React.ReactNode }) {
  const hasResults = results.length > 0;
  const showEmpty = !isLoading && !hasResults && searchValue.trim().length > 0;

  return (
    <Popover open={isOpen} onOpenChange={onOpenChange}>
      <PopoverTrigger asChild>
        {children}
      </PopoverTrigger>
      <PopoverContent
        className="p-0 w-[min(560px,95vw)] max-w-lg"
        align="start"
        side="bottom"
        sideOffset={4}
      >
        <Command shouldFilter={false}>
          <CommandInput 
            placeholder={isLoading ? "Searching..." : "Type to search"}
            value={searchValue}
            onValueChange={onSearchValueChange}
            className="border-none focus:ring-0"
            aria-label="Search input"
          />
          <CommandList>
            {showEmpty && (
              <CommandEmpty>
                No locations found for "{searchValue}"
              </CommandEmpty>
            )}
            
            {isLoading && (
              <div className="px-4 py-3 text-sm text-muted-foreground">
                Searching...
              </div>
            )}
            
            {hasResults && (
              <CommandGroup heading="Suggestions">
                {results.map((result, index) => {
                  const { primary, secondary } = getSearchResultDisplayText(result);
                  const key = getSearchResultKey(result, index);
                  
                  return (
                    <CommandItem
                      key={key}
                      value={`${result.name}, ${result.admin1 || result.country}`}
                      onSelect={() => onSelect(result)}
                      className="cursor-pointer"
                      aria-label={`Select ${primary}, ${secondary}`}
                    >
                      <div className="flex flex-col w-full">
                        <span className="font-medium text-foreground">
                          {primary}
                        </span>
                        {secondary && (
                          <span className="text-xs text-muted-foreground">
                            {secondary}
                          </span>
                        )}
                      </div>
                    </CommandItem>
                  );
                })}
              </CommandGroup>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
