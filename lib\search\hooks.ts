/**
 * Custom hooks for search functionality
 */

"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import type { 
  SearchResult, 
  UseSearchSuggestionsReturn, 
  UseGeolocationReturn, 
  UseDebounceReturn,
  GeolocationCoords 
} from "@/lib/types";
import { searchLocations } from "./api";
import { 
  sanitizeSearchQuery, 
  isValidSearchQuery, 
  processSearchResults, 
  sortSearchResults,
  DEFAULT_SEARCH_DEBOUNCE_DELAY,
  isValidCoordinates
} from "./utils";

/**
 * Hook for debouncing values with loading state
 */
export function useDebounce<T>(
  value: T, 
  delay: number = DEFAULT_SEARCH_DEBOUNCE_DELAY
): UseDebounceReturn<T> {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);
  const [isDebouncing, setIsDebouncing] = useState(false);

  useEffect(() => {
    setIsDebouncing(true);
    
    const handler = setTimeout(() => {
      setDebouncedValue(value);
      setIsDebouncing(false);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return { debouncedValue, isDebouncing };
}

/**
 * Hook for managing search suggestions with debouncing and API calls
 */
export function useSearchSuggestions(
  delay: number = DEFAULT_SEARCH_DEBOUNCE_DELAY
): UseSearchSuggestionsReturn {
  const [searchValue, setSearchValue] = useState("");
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { debouncedValue: debouncedQuery } = useDebounce(searchValue, delay);
  const abortControllerRef = useRef<AbortController | null>(null);

  const clearResults = useCallback(() => {
    setResults([]);
    setError(null);
  }, []);

  useEffect(() => {
    const performSearch = async () => {
      const sanitizedQuery = sanitizeSearchQuery(debouncedQuery);
      
      if (!isValidSearchQuery(sanitizedQuery)) {
        clearResults();
        return;
      }

      // Cancel previous request
      abortControllerRef.current?.abort();
      
      // Create new abort controller
      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      setIsLoading(true);
      setError(null);

      try {
        const searchResults = await searchLocations(sanitizedQuery, abortController.signal);
        
        // Only update if this request wasn't cancelled
        if (!abortController.signal.aborted) {
          const processedResults = processSearchResults(searchResults);
          const sortedResults = sortSearchResults(processedResults, sanitizedQuery);
          setResults(sortedResults);
        }
      } catch (err) {
        if (!abortController.signal.aborted) {
          const errorMessage = err instanceof Error ? err.message : "Search failed";
          setError(errorMessage);
          setResults([]);
        }
      } finally {
        if (!abortController.signal.aborted) {
          setIsLoading(false);
        }
      }
    };

    performSearch();

    // Cleanup function
    return () => {
      abortControllerRef.current?.abort();
    };
  }, [debouncedQuery, clearResults]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      abortControllerRef.current?.abort();
    };
  }, []);

  return {
    results,
    isLoading,
    error,
    searchValue,
    setSearchValue,
    clearResults
  };
}

/**
 * Hook for managing geolocation functionality
 */
export function useGeolocation(): UseGeolocationReturn {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const isSupported = typeof navigator !== 'undefined' && 'geolocation' in navigator;

  const getCurrentLocation = useCallback((): Promise<GeolocationCoords> => {
    return new Promise((resolve, reject) => {
      if (!isSupported) {
        const error = "Geolocation is not supported by this browser";
        setError(error);
        reject(new Error(error));
        return;
      }

      setIsLoading(true);
      setError(null);

      navigator.geolocation.getCurrentPosition(
        (position) => {
          const coords = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude
          };

          if (!isValidCoordinates(coords.latitude, coords.longitude)) {
            const error = "Invalid coordinates received";
            setError(error);
            setIsLoading(false);
            reject(new Error(error));
            return;
          }

          setIsLoading(false);
          resolve(coords);
        },
        (error) => {
          let errorMessage = "Failed to get location";
          
          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = "Location access denied by user";
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage = "Location information unavailable";
              break;
            case error.TIMEOUT:
              errorMessage = "Location request timed out";
              break;
          }

          setError(errorMessage);
          setIsLoading(false);
          reject(new Error(errorMessage));
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000 // 5 minutes
        }
      );
    });
  }, [isSupported]);

  return {
    getCurrentLocation,
    isLoading,
    error,
    isSupported
  };
}
