"use client";

import React, { useState, useEffect } from "react";
import { WeatherCardSkeleton } from "@/components/WeatherCardSkeleton";
import HeroWeatherCard from "@/components/HeroWeatherCard";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";
import { MapPin, Star, Loader2, Sun } from "lucide-react";
import type { WeatherResponse, Units, SearchSource } from "@/lib/types";
import { SearchBox } from "@/components/search";

export default function Home() {
  const [query, setQuery] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<WeatherResponse | null>(null);
  const [unit, setUnit] = useState<Units>("metric");
  const [favorites, setFavorites] = useState<string[]>([]);

  // Load favorites from localStorage after component mounts to prevent hydration mismatch
  useEffect(() => {
    try {
      const raw = localStorage.getItem("favorites");
      if (raw) {
        setFavorites(JSON.parse(raw) as string[]);
      }
    } catch {
      // Ignore localStorage errors
    }
  }, []);
  const [lastSource, setLastSource] = useState<SearchSource | null>(null);

  async function onSearch(searchQuery?: string) {
    const queryToUse = searchQuery || query;
    if (!queryToUse.trim()) return;

    setLoading(true);
    setError(null);

    try {
      const res = await fetch(`/api/weather?q=${encodeURIComponent(queryToUse)}&unit=${unit}`, { cache: "no-store" });
      const json = await res.json();
      if (!res.ok) {
        setError(json.error || "Failed to fetch weather");
        setData(null);
        return;
      }
      setData(json);
      setLastSource({ type: "query", query: queryToUse });
      try { localStorage.setItem("lastWeather", JSON.stringify(json)); } catch {}
    } catch {
      try {
        const cached = localStorage.getItem("lastWeather");
        if (cached) setData(JSON.parse(cached) as WeatherResponse);
        else setError("Network error");
      } catch {
        setError("Network error");
      }
    } finally {
      setLoading(false);
    }
  }

  // GPS handled inside SearchBox's onSelectCoords callback

  function toggleFavorite(city: string) {
    setFavorites((prev) => {
      const exists = prev.includes(city);
      const next = exists ? prev.filter((c) => c !== city) : [...prev, city];
      try {
        localStorage.setItem("favorites", JSON.stringify(next));
      } catch {}
      return next;
    });
  }

  // Refetch when unit changes
  useEffect(() => {
    if (!lastSource) return;
    (async () => {
      try {
        let url = "";
        if (lastSource.type === "query") {
          url = `/api/weather?q=${encodeURIComponent(lastSource.query)}&unit=${unit}`;
        } else if (lastSource.type === "coordinates" || lastSource.type === "geolocation") {
          url = `/api/weather?lat=${lastSource.latitude}&lon=${lastSource.longitude}&unit=${unit}`;
        }
        const res = await fetch(url, { cache: "no-store" });
        const json = await res.json();
        if (res.ok) setData(json);
      } catch {}
    })();
  }, [unit, lastSource]);

  return (
    <div className="min-h-screen bg-background text-foreground transition-colors">
      <main className="container mx-auto max-w-6xl px-4 sm:px-6 lg:px-8 py-6 sm:py-8 lg:py-10">
        {/* Header Section */}
        <div className="space-y-4">
          <div>
            <h1 className="text-heading-lg">Weather</h1>
            <p className="text-muted-foreground mt-1 text-body">
              Search a city or use your location to view weather details.
            </p>
          </div>

          {/* Search Section */}
          <div className="space-y-4">
            <SearchBox
              unit={unit}
              loading={loading}
              onSelectQuery={(text) => {
                setQuery(text);
                // Trigger search immediately when a query is selected
                if (!loading) {
                  onSearch(text);
                }
              }}
              onSelectCoords={async (latitude, longitude) => {
                setQuery("");
                setLoading(true);
                setError(null);

                try {
                  const res = await fetch(
                    `/api/weather?lat=${latitude}&lon=${longitude}&unit=${unit}`,
                    { cache: "no-store" }
                  );
                  const json = await res.json();

                  if (!res.ok) {
                    setError(json.error || "Failed to fetch weather");
                    setData(null);
                    return;
                  }

                  setData(json);
                  setLastSource({ type: "coordinates", latitude, longitude });
                  try {
                    localStorage.setItem("lastWeather", JSON.stringify(json));
                  } catch {}
                } catch {
                  try {
                    const cached = localStorage.getItem("lastWeather");
                    if (cached) {
                      setData(JSON.parse(cached) as WeatherResponse);
                    } else {
                      setError("Network error");
                    }
                  } catch {
                    setError("Network error");
                  }
                } finally {
                  setLoading(false);
                }
              }}
            />


          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mt-6 p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0 w-5 h-5 rounded-full bg-destructive/20 flex items-center justify-center mt-0.5">
                <span className="text-destructive text-xs font-bold">!</span>
              </div>
              <div className="flex-1">
                <h4 className="text-destructive font-medium text-sm mb-1">Error</h4>
                <p className="text-destructive/80 text-sm">{error}</p>
                <p className="text-destructive/60 text-xs mt-2">
                  Please check your internet connection and try again.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Loading State */}
        {loading && !data && (
          <div className="mt-8 space-y-6">
            <div className="flex items-center justify-center">
              <div className="flex items-center gap-2 text-muted-foreground">
                <Loader2 className="h-5 w-5 animate-spin" />
                <span>Loading weather data...</span>
              </div>
            </div>
            <WeatherCardSkeleton />
          </div>
        )}

        {/* Weather Data Display */}
        {data && !loading && (
          <div className="mt-8">
            <div className="grid gap-6 lg:grid-cols-12">
              {/* Left column: Hero + Hourly (desktop) */}
              <div className="space-y-6 lg:col-span-7">
                {/* Main Weather Hero Section */}
                <div className="relative">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <MapPin className="h-4 w-4" />
                      <span className="text-body">
                        {data.country ? `${data.name}, ${data.country}` : data.name}
                      </span>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => toggleFavorite(data.name)}
                      title="Toggle favorite"
                      className="hover:bg-secondary/80"
                    >
                      <Star className={`h-5 w-5 ${favorites.includes(data.name) ? "fill-current text-yellow-500" : ""}`} />
                    </Button>
                  </div>

                  <HeroWeatherCard
                    image={data.image}
                    condition={data.condition}
                    isDay={data.current.is_day}
                    name={data.name}
                    country={data.country}
                    temperature={data.current.temperature}
                    unit={unit}
                    onUnitChange={(newUnit) => setUnit(newUnit)}
                    windspeed={data.current.windspeed}
                    humidity={data.details?.humidity ?? null}
                    pressure={data.details?.pressure ?? null}
                  />
                </div>

                {/* Hourly Forecast */}
                {data.hourly && data.hourly.length > 0 && (
                  <div className="space-y-4">
                    <h3 className="text-heading-sm">Next 24 Hours</h3>
                    <ScrollArea className="w-full custom-scrollbar">
                      <div className="flex gap-4 pb-3">
                        {data.hourly.map((h, i) => (
                          <div key={i} className="min-w-20 sm:min-w-24 rounded-xl border bg-card p-3 text-center shadow-sm transition-smooth hover:shadow-md">
                            <div className="text-caption text-muted-foreground mb-2">
                              {new Date(h.time).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
                            </div>
                            <div className="text-body font-semibold">
                              {Math.round(h.temperature)}°
                            </div>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </div>
                )}
              </div>

              {/* Right column: Details + AQI/Sunrise (desktop) */}
              <div className="space-y-6 lg:col-span-5">
                {/* Additional Weather Details */}
                {(data.details?.feels_like != null || data.details?.humidity != null || data.details?.pressure != null) && (
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    {data.details?.feels_like != null && (
                      <div className="rounded-xl bg-card border p-4 text-center">
                        <div className="text-body-sm text-muted-foreground mb-2">Feels like</div>
                        <div className="text-heading-md font-semibold">
                          {Math.round(data.details.feels_like)}{unit === "imperial" ? "°F" : "°C"}
                        </div>
                      </div>
                    )}
                    {data.details?.humidity != null && (
                      <div className="rounded-xl bg-card border p-4 text-center">
                        <div className="text-body-sm text-muted-foreground mb-2">Humidity</div>
                        <div className="text-heading-md font-semibold">
                          {Math.round(data.details.humidity)}%
                        </div>
                      </div>
                    )}
                    {data.details?.pressure != null && (
                      <div className="rounded-xl bg-card border p-4 text-center">
                        <div className="text-body-sm text-muted-foreground mb-2">Pressure</div>
                        <div className="text-heading-md font-semibold">
                          {Math.round(data.details.pressure)} hPa
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {/* Air Quality Index */}
                {data.aqi && (
                  <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 p-4 bg-secondary/30 rounded-xl">
                    <Badge variant="secondary" className="w-fit">AQI: {data.aqi.index}</Badge>
                    <span className="text-body-sm text-muted-foreground">{data.aqi.category}</span>
                  </div>
                )}

                {/* Sunrise/Sunset */}
                {data.sunrise && data.sunset && (
                  <div className="p-4 bg-secondary/30 rounded-xl">
                    <div className="text-body-sm text-muted-foreground text-center sm:text-left">
                      <span className="inline-flex items-center gap-2">
                        <Sun className="h-4 w-4" />
                        Sunrise: {new Date(data.sunrise).toLocaleTimeString()}
                      </span>
                      <span className="mx-2 hidden sm:inline">·</span>
                      <span className="block sm:inline mt-1 sm:mt-0">
                        Sunset: {new Date(data.sunset).toLocaleTimeString()}
                      </span>
                    </div>
                  </div>
                )}
              </div>

              {/* Daily Forecast: full-width on desktop */}
              {data.daily && data.daily.length > 0 && (
                <div className="lg:col-span-12 space-y-4">
                  <h3 className="text-heading-sm">7-Day Forecast</h3>
                  <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-7 gap-3">
                    {data.daily.map((d, i) => (
                      <div key={i} className="rounded-xl border bg-card p-4 text-center shadow-sm transition-smooth hover:shadow-md">
                        <div className="text-caption text-muted-foreground mb-2">
                          {new Date(d.date).toLocaleDateString(undefined, { weekday: "short" })}
                        </div>
                        <div className="space-y-1">
                          <div className="text-body font-semibold">{Math.round(d.max)}°</div>
                          <div className="text-body-sm text-muted-foreground">{Math.round(d.min)}°</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Favorites Section */}
        {favorites.length > 0 && (
          <div className="mt-10 space-y-4">
            <h3 className="text-heading-sm flex items-center gap-2">
              <Star className="h-4 w-4 text-yellow-500" />
              Favorites
            </h3>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-2">
              {favorites.map((city) => (
                <Badge
                  key={city}
                  variant="secondary"
                  className="cursor-pointer hover:bg-secondary/80 transition-colors p-2 justify-center text-center"
                  onClick={() => { setQuery(city); onSearch(city); }}
                >
                  <MapPin className="h-3 w-3 mr-1 shrink-0" />
                  <span className="truncate">{city}</span>
                </Badge>
              ))}
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
