"use client";

import Image from "next/image";
import { useMemo } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Cloud, CloudRain, Sun, Snowflake, Droplets, Thermometer, Gauge } from "lucide-react";

type WeatherCardProps = {
  image: string;
  condition: string;
  name: string;
  country?: string;
  temperature: number;
  windspeed: number;
  feelsLike?: number | null;
  humidity?: number | null;
  pressure?: number | null;
  unit: "metric" | "imperial";
  imageHeight?: number;
};

export default function WeatherCard(props: WeatherCardProps) {
  const locationLabel = useMemo(() => {
    if (props.country) return `${props.name}, ${props.country}`;
    return props.name;
  }, [props.name, props.country]);

  const ConditionIcon = useMemo(() => {
    const c = props.condition.toLowerCase();
    if (c.includes("rain") || c.includes("drizzle")) return CloudRain;
    if (c.includes("snow")) return Snowflake;
    if (c.includes("cloud")) return Cloud;
    return Sun;
  }, [props.condition]);

  return (
    <Card className="w-full max-w-4xl overflow-hidden border border-border shadow-md hover:shadow-lg transition-smooth">
      <CardContent className="p-0">
        <div className="flex flex-col lg:grid lg:grid-cols-2">
          {/* Image Section - Responsive height */}
          <div className="relative h-48 sm:h-56 md:h-64 lg:h-80">
            <Image
              src={props.image}
              alt={props.condition}
              fill
              className="object-cover"
              sizes="(max-width: 1024px) 100vw, 50vw"
              priority
            />
          </div>

          {/* Content Section */}
          <div className="p-4 sm:p-6 flex flex-col gap-4">
            {/* Header */}
            <div>
              <div>
                <h2 className="text-heading-md">
                  {locationLabel}
                </h2>
              </div>
              <p className="text-muted-foreground mt-1 flex items-center gap-2 text-body">
                <ConditionIcon className="h-4 w-4" /> {props.condition}
              </p>
            </div>

            {/* Main Weather Stats */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
              <div className="rounded-lg bg-secondary p-3 sm:p-4">
                <p className="text-body-sm text-muted-foreground">Temperature</p>
                <p className="text-display-sm font-bold">
                  {Math.round(props.temperature)}{props.unit === "imperial" ? "°F" : "°C"}
                </p>
              </div>
              <div className="rounded-lg bg-secondary p-3 sm:p-4">
                <p className="text-body-sm text-muted-foreground">Wind</p>
                <p className="text-display-sm font-bold">
                  {Math.round(props.windspeed)} {props.unit === "imperial" ? "mph" : "km/h"}
                </p>
              </div>
            </div>

            <Separator />

            {/* Additional Details - Mobile-first responsive grid */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
              <div className="rounded-lg bg-secondary p-3">
                <div className="flex items-center gap-2 text-muted-foreground mb-1 text-body-sm">
                  <Thermometer className="h-4 w-4" />
                  <span>Feels like</span>
                </div>
                <div className="text-heading-sm font-semibold">
                  {props.feelsLike != null
                    ? `${Math.round(props.feelsLike)}${props.unit === "imperial" ? "°F" : "°C"}`
                    : "-"}
                </div>
              </div>
              <div className="rounded-lg bg-secondary p-3">
                <div className="flex items-center gap-2 text-muted-foreground mb-1 text-body-sm">
                  <Droplets className="h-4 w-4" />
                  <span>Humidity</span>
                </div>
                <div className="text-heading-sm font-semibold">
                  {props.humidity != null ? `${Math.round(props.humidity)}%` : "-"}
                </div>
              </div>
              <div className="rounded-lg bg-secondary p-3">
                <div className="flex items-center gap-2 text-muted-foreground mb-1 text-body-sm">
                  <Gauge className="h-4 w-4" />
                  <span>Pressure</span>
                </div>
                <div className="text-heading-sm font-semibold">
                  {props.pressure != null ? `${Math.round(props.pressure)} hPa` : "-"}
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}


