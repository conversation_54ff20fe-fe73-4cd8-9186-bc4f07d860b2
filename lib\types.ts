/**
 * Centralized type definitions for the weather app
 */

// Weather condition types
export type Condition = "Sunny" | "Cloudy" | "Rain" | "Snow" | "Fog";

// Unit system types
export type Units = "metric" | "imperial";

// Search-related types
export interface SearchResult {
  name: string;
  country: string;
  admin1: string;
  latitude: number;
  longitude: number;
}

export interface SearchResponse {
  results: SearchResult[];
}

// Geocoding API response types (from Open-Meteo)
export interface GeocodingApiResult {
  id?: number;
  name: string;
  country?: string;
  admin1?: string;
  latitude: number;
  longitude: number;
}

export interface GeocodingApiResponse {
  results?: GeocodingApiResult[];
}

// Weather data types
export interface WeatherCurrent {
  temperature: number;
  windspeed: number;
  weathercode: number;
  is_day: number;
  time: string;
}

export interface WeatherDetails {
  feels_like: number | null;
  humidity: number | null;
  pressure: number | null;
}

export interface WeatherHourly {
  time: string;
  temperature: number;
  weathercode: number;
}

export interface WeatherDaily {
  date: string;
  max: number;
  min: number;
  weathercode: number;
}

export interface AirQualityIndex {
  index: number;
  category: string;
}

export interface WeatherResponse {
  name: string;
  country: string;
  latitude: number;
  longitude: number;
  unit: Units;
  current: WeatherCurrent;
  details?: WeatherDetails;
  sunrise?: string | null;
  sunset?: string | null;
  hourly?: WeatherHourly[];
  daily?: WeatherDaily[];
  aqi?: AirQualityIndex | null;
  image: string;
  condition: Condition;
}

// Search state types
export interface SearchState {
  query: string;
  isOpen: boolean;
  results: SearchResult[];
  isLoading: boolean;
  error: string | null;
}

// Geolocation types
export interface GeolocationCoords {
  latitude: number;
  longitude: number;
}

export interface GeolocationState {
  isLoading: boolean;
  error: string | null;
  coords: GeolocationCoords | null;
}

// Search source tracking
export type SearchSource = 
  | { type: "query"; query: string }
  | { type: "coordinates"; latitude: number; longitude: number }
  | { type: "geolocation"; latitude: number; longitude: number };

// Component prop types
export interface SearchBoxProps {
  unit: Units;
  loading: boolean;
  onSelectQuery: (query: string) => void;
  onSelectCoords: (latitude: number, longitude: number) => void;
  className?: string;
  placeholder?: string;
}

export interface SearchInputProps {
  value: string;
  onChange: (value: string) => void;
  onFocus: () => void;
  onKeyDown: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  placeholder?: string;
  isLoading?: boolean;
  className?: string;
}

export interface SearchSuggestionsProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  results: SearchResult[];
  isLoading: boolean;
  onSelect: (result: SearchResult) => void;
  searchValue: string;
  onSearchValueChange: (value: string) => void;
}

export interface LocationButtonProps {
  onLocationSelect: (latitude: number, longitude: number) => void;
  isLoading?: boolean;
  className?: string;
}

// API error types
export interface ApiError {
  error: string;
  status?: number;
}

// Hook return types
export interface UseSearchSuggestionsReturn {
  results: SearchResult[];
  isLoading: boolean;
  error: string | null;
  searchValue: string;
  setSearchValue: (value: string) => void;
  clearResults: () => void;
}

export interface UseGeolocationReturn {
  getCurrentLocation: () => Promise<GeolocationCoords>;
  isLoading: boolean;
  error: string | null;
  isSupported: boolean;
}

export interface UseDebounceReturn<T> {
  debouncedValue: T;
  isDebouncing: boolean;
}
